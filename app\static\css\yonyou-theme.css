/* 用友风格主题样式 */
:root {
    --primary-color: #1e88e5;
    --primary-dark: #1565c0;
    --primary-light: #bbdefb;
    --accent-color: #ff9800;
    --text-primary: #333333;
    --text-secondary: #666666;
    --border-color: #e0e0e0;
    --background-light: #f5f7fa;
    --background-white: #ffffff;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    --font-size-base: 13px;
    --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    --border-radius: 4px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --transition-base: all 0.3s ease;
    --yonyou-font-family: "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", "Helvetica Neue", Helvetica, Arial, sans-serif;
    --yonyou-font-size: 13px;
    --yonyou-line-height: 1.5;
    --yonyou-color-primary: #1890ff;
    --yonyou-color-success: #52c41a;
    --yonyou-color-warning: #faad14;
    --yonyou-color-danger: #f5222d;
    --yonyou-color-text: #333333;
    --yonyou-color-text-secondary: #666666;
    --yonyou-color-border: #e8e8e8;
    --yonyou-color-background: #f5f5f5;
    --yonyou-border-radius: 4px;
    --yonyou-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 基础样式 */
body {
    font-family: var(--yonyou-font-family);
    font-size: var(--yonyou-font-size);
    line-height: var(--yonyou-line-height);
    color: var(--yonyou-color-text);
    background-color: var(--yonyou-color-background);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

/* 按钮样式 */
.btn {
    font-size: var(--yonyou-font-size);
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition-base);
}

/* 表单样式 */
.form-control {
    font-size: var(--yonyou-font-size);
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

/* 表格样式 */
.table {
    font-size: var(--yonyou-font-size);
    width: 100%;
    margin-bottom: 1rem;
    background-color: var(--background-white);
    border-collapse: collapse;
}

.table th,
.table td {
    font-size: var(--font-size-base);
    padding: 0.75rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

/* 卡片样式 */
.card {
    font-size: var(--yonyou-font-size);
    background-color: var(--background-white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.card-header {
    font-size: var(--font-size-base);
    padding: 0.75rem 1.25rem;
    background-color: var(--background-white);
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    font-size: var(--font-size-base);
    padding: 1.25rem;
}

/* 导航样式 */
.nav-link {
    font-size: var(--yonyou-font-size);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    text-decoration: none;
}

/* 标签样式 */
.badge {
    font-size: var(--yonyou-font-size);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
}

/* 警告框样式 */
.alert {
    font-size: var(--yonyou-font-size);
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

/* 模态框样式 */
.modal-title {
    font-size: var(--yonyou-font-size);
    font-weight: 500;
}

.modal-body {
    font-size: var(--yonyou-font-size);
    padding: 1.25rem;
}

/* 分页样式 */
.pagination {
    font-size: var(--yonyou-font-size);
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: var(--border-radius);
}

.page-link {
    font-size: var(--yonyou-font-size);
    padding: 0.5rem 0.75rem;
    color: var(--primary-color);
    background-color: var(--background-white);
    border: 1px solid var(--border-color);
}

/* 工具提示样式 */
.tooltip {
    font-size: var(--yonyou-font-size);
}

/* 下拉菜单样式 */
.dropdown-menu {
    font-size: var(--font-size-base);
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0;
    background-color: var(--background-white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.dropdown-item {
    font-size: var(--yonyou-font-size);
    padding: 0.25rem 1.5rem;
    color: var(--text-primary);
}

/* 进度条样式 */
.progress {
    font-size: var(--yonyou-font-size);
    height: 1rem;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
}

/* 列表组样式 */
.list-group-item {
    font-size: var(--yonyou-font-size);
    padding: 0.75rem 1.25rem;
    background-color: var(--background-white);
    border: 1px solid var(--border-color);
}

/* 面包屑导航样式 */
.breadcrumb {
    font-size: var(--yonyou-font-size);
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    background-color: var(--background-light);
    border-radius: var(--border-radius);
}

/* 代码样式 */
code {
    font-size: var(--font-size-base);
    color: var(--error-color);
    background-color: var(--background-light);
    padding: 0.2rem 0.4rem;
    border-radius: var(--border-radius);
}

/* 引用样式 */
blockquote {
    font-size: var(--font-size-base);
    padding: 0.5rem 1rem;
    margin: 0 0 1rem;
    border-left: 4px solid var(--primary-color);
    background-color: var(--background-light);
}

/* 响应式调整 */
@media (max-width: 768px) {
    :root {
        --font-size-base: 13px;
    }
    
    .table th,
    .table td {
        font-size: var(--font-size-base);
        padding: 0.5rem;
    }
    
    .btn {
        font-size: var(--font-size-base);
        padding: 0.25rem 0.5rem;
    }
    
    .form-control {
        font-size: var(--font-size-base);
        padding: 0.25rem 0.5rem;
    }
} 