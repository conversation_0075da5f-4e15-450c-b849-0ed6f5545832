{% extends "base.html" %}

{% block title %}财务管理{% endblock %}

{% block extra_css %}
<style>
    .financial-card {
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 1.5rem;
    }
    
    .financial-card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
        padding: 0.75rem 1.25rem;
        font-weight: 600;
        color: #5a5c69;
    }
    
    .financial-card-body {
        padding: 1.25rem;
    }
    
    .financial-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }
    
    .financial-stat-item {
        text-align: center;
        flex: 1;
        padding: 0.5rem;
    }
    
    .financial-stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #5a5c69;
    }
    
    .financial-stat-label {
        font-size: 0.875rem;
        color: #858796;
        margin-top: 0.25rem;
    }
    
    .financial-amount {
        font-weight: 600;
        font-family: 'Courier New', monospace;
    }
    
    .financial-amount.positive {
        color: #1cc88a;
    }
    
    .financial-amount.negative {
        color: #e74a3b;
    }
    
    .financial-amount.zero {
        color: #858796;
    }
    
    .financial-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .financial-status.draft {
        background-color: #f6c23e;
        color: #fff;
    }
    
    .financial-status.pending {
        background-color: #36b9cc;
        color: #fff;
    }
    
    .financial-status.approved {
        background-color: #1cc88a;
        color: #fff;
    }
    
    .financial-status.posted {
        background-color: #5a5c69;
        color: #fff;
    }
    
    .financial-status.unpaid {
        background-color: #e74a3b;
        color: #fff;
    }
    
    .financial-status.partial {
        background-color: #f6c23e;
        color: #fff;
    }
    
    .financial-status.paid {
        background-color: #1cc88a;
        color: #fff;
    }
    
    .financial-table {
        font-size: 0.875rem;
    }
    
    .financial-table th {
        background-color: #f8f9fc;
        border-color: #e3e6f0;
        font-weight: 600;
        color: #5a5c69;
    }
    
    .financial-table td {
        border-color: #e3e6f0;
        vertical-align: middle;
    }
    
    .financial-form .form-group label {
        font-weight: 600;
        color: #5a5c69;
    }
    
    .financial-form .form-control {
        border-color: #d1d3e2;
    }
    
    .financial-form .form-control:focus {
        border-color: #bac8f3;
        box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    }
    
    .financial-breadcrumb {
        background-color: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }
    
    .financial-breadcrumb .breadcrumb-item {
        font-size: 0.875rem;
    }
    
    .financial-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: #858796;
    }
    
    .financial-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }
    
    .financial-actions .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .financial-search-form {
        background-color: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .financial-search-form .form-row {
        align-items: end;
    }
    
    .financial-pagination {
        justify-content: center;
        margin-top: 1.5rem;
    }
    
    .financial-pagination .page-link {
        color: #5a5c69;
        border-color: #d1d3e2;
    }
    
    .financial-pagination .page-item.active .page-link {
        background-color: #4e73df;
        border-color: #4e73df;
    }
    
    .financial-voucher-details {
        background-color: #f8f9fc;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .financial-voucher-details table {
        margin-bottom: 0;
    }
    
    .financial-summary-box {
        background: linear-gradient(90deg, #4e73df 0%, #224abe 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 0.35rem;
        margin-bottom: 1.5rem;
    }
    
    .financial-summary-box h4 {
        margin-bottom: 0.5rem;
        font-weight: 700;
    }
    
    .financial-summary-box .summary-item {
        display: inline-block;
        margin-right: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .financial-summary-box .summary-label {
        font-size: 0.875rem;
        opacity: 0.8;
    }
    
    .financial-summary-box .summary-value {
        font-size: 1.25rem;
        font-weight: 700;
        font-family: 'Courier New', monospace;
    }
    
    @media (max-width: 768px) {
        .financial-stats {
            flex-direction: column;
        }
        
        .financial-actions {
            flex-direction: column;
            align-items: stretch;
        }
        
        .financial-actions .btn {
            margin-bottom: 0.5rem;
        }
        
        .financial-search-form .form-row {
            flex-direction: column;
        }
        
        .financial-search-form .form-group {
            margin-bottom: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb" class="financial-breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></li>
            {% block breadcrumb %}{% endblock %}
        </ol>
    </nav>
    
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{% block page_title %}财务管理{% endblock %}</h1>
        {% block page_actions %}{% endblock %}
    </div>
    
    <!-- 页面内容 -->
    {% block financial_content %}{% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 财务模块通用JavaScript函数

// 格式化金额显示
function formatAmount(amount) {
    if (amount === null || amount === undefined) return '0.00';
    return parseFloat(amount).toFixed(2);
}

// 格式化金额为中文显示
function formatAmountChinese(amount) {
    const formatted = formatAmount(amount);
    return '¥' + formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        '草稿': 'draft',
        '待审核': 'pending',
        '已审核': 'approved',
        '已记账': 'posted',
        '未付款': 'unpaid',
        '部分付款': 'partial',
        '已付款': 'paid'
    };
    return statusMap[status] || 'draft';
}

// 确认删除对话框
function confirmDelete(message) {
    return confirm(message || '确定要删除这条记录吗？此操作不可撤销。');
}

// 显示加载状态
function showLoading(element) {
    if (element) {
        element.disabled = true;
        const originalText = element.textContent;
        element.textContent = '处理中...';
        element.dataset.originalText = originalText;
    }
}

// 隐藏加载状态
function hideLoading(element) {
    if (element && element.dataset.originalText) {
        element.disabled = false;
        element.textContent = element.dataset.originalText;
        delete element.dataset.originalText;
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有金额显示
    document.querySelectorAll('.financial-amount').forEach(function(element) {
        const amount = parseFloat(element.textContent);
        element.textContent = formatAmountChinese(amount);
        
        // 添加正负数样式
        if (amount > 0) {
            element.classList.add('positive');
        } else if (amount < 0) {
            element.classList.add('negative');
        } else {
            element.classList.add('zero');
        }
    });
    
    // 初始化所有状态显示
    document.querySelectorAll('.financial-status').forEach(function(element) {
        const status = element.textContent.trim();
        element.classList.add(getStatusClass(status));
    });
    
    // 初始化确认删除按钮
    document.querySelectorAll('[data-confirm-delete]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            const message = this.dataset.confirmDelete;
            if (!confirmDelete(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
});
</script>
{% block financial_js %}{% endblock %}
{% endblock %}
